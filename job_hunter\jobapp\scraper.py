# scraper.py

import random
import sqlite3
import logging
import os
import platform
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.common.exceptions import WebDriverException, TimeoutException
from webdriver_manager.chrome import ChromeDriverManager
import requests
from bs4 import BeautifulSoup
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
LOGGER = logging.getLogger(__name__)
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
]
CACHE_DB = 'jobs_cache.db'


def get_chrome_driver():
    """
    Create a Chrome WebDriver with robust configuration.

    Returns:
        WebDriver instance or None if failed
    """
    try:
        # Chrome options for stability
        opts = Options()

        # Basic stability options
        opts.add_argument('--no-sandbox')
        opts.add_argument('--disable-dev-shm-usage')
        opts.add_argument('--disable-gpu')
        opts.add_argument('--disable-web-security')
        opts.add_argument('--disable-features=VizDisplayCompositor')
        opts.add_argument('--disable-extensions')
        opts.add_argument('--disable-plugins')
        opts.add_argument('--disable-images')
        opts.add_argument('--disable-javascript')

        # Memory and performance options
        opts.add_argument('--memory-pressure-off')
        opts.add_argument('--max_old_space_size=4096')
        opts.add_argument('--disable-background-timer-throttling')
        opts.add_argument('--disable-renderer-backgrounding')
        opts.add_argument('--disable-backgrounding-occluded-windows')

        # User agent
        ua = random.choice(USER_AGENTS)
        opts.add_argument(f'--user-agent={ua}')
        LOGGER.info(f'Using User Agent: {ua}')

        # Headless mode for stability
        opts.add_argument('--headless=new')

        # Window size
        opts.add_argument('--window-size=1920,1080')

        # Try to create service with webdriver-manager (automatic ChromeDriver management)
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=opts)
            LOGGER.info("Chrome driver created successfully with webdriver-manager")
            return driver
        except Exception as e:
            LOGGER.warning(f"webdriver-manager failed: {e}")

        # Try with default service (Selenium's built-in driver management)
        try:
            service = Service()
            driver = webdriver.Chrome(service=service, options=opts)
            LOGGER.info("Chrome driver created successfully with default service")
            return driver
        except WebDriverException as e:
            LOGGER.warning(f"Default service failed: {e}")

        # Try with explicit ChromeDriver path if auto-management fails
        possible_paths = [
            'chromedriver.exe',
            'chromedriver',
            os.path.join(os.getcwd(), 'chromedriver.exe'),
            os.path.join(os.getcwd(), 'chromedriver'),
        ]

        for path in possible_paths:
            try:
                if os.path.exists(path):
                    service = Service(executable_path=path)
                    driver = webdriver.Chrome(service=service, options=opts)
                    LOGGER.info(f"Chrome driver created successfully with ChromeDriver at: {path}")
                    return driver
            except Exception as e:
                LOGGER.warning(f"Failed to create driver with path {path}: {e}")
                continue

        # If all else fails, try without service
        try:
            driver = webdriver.Chrome(options=opts)
            LOGGER.info("Chrome driver created successfully without explicit service")
            return driver
        except Exception as e:
            LOGGER.error(f"Failed to create Chrome driver without service: {e}")

        return None

    except Exception as e:
        LOGGER.error(f"Critical error creating Chrome driver: {e}")
        return None


def scrape_jobs_fallback(keywords, locations, pages=1):
    """
    Fallback scraping method using mock data.
    This is used when Selenium scraping fails.

    Args:
        keywords: List of job keywords to search for
        locations: List of locations to search in
        pages: Number of pages to scrape per keyword/location combination

    Returns:
        List of job dictionaries with title, company, location, and URL
    """
    LOGGER.info("Using fallback scraping method (mock data)")
    jobs = []

    # Create some mock job data for testing when scraping fails
    mock_companies = [
        "Tech Solutions Inc", "Digital Innovations", "Global Systems",
        "Future Technologies", "Smart Solutions", "Advanced Computing",
        "Data Dynamics", "Cloud Systems", "AI Innovations", "Web Solutions"
    ]

    mock_titles = [
        "Senior Python Developer", "Full Stack Developer", "Software Engineer",
        "Backend Developer", "Python Engineer", "Web Developer",
        "Data Engineer", "DevOps Engineer", "Software Architect", "Technical Lead"
    ]

    for kw in keywords:
        for loc in locations:
            for i in range(min(5, pages * 10)):  # Generate 5-10 mock jobs per keyword/location
                job = {
                    'keyword': kw,
                    'title': random.choice(mock_titles),
                    'company': random.choice(mock_companies),
                    'location': loc,
                    'url': f"https://example.com/job/{i+1}"
                }
                jobs.append(job)

    LOGGER.info(f"Generated {len(jobs)} mock jobs for testing")
    return jobs

def init_db():
    """Initialize the SQLite database for caching scraped URLs."""
    conn = sqlite3.connect(CACHE_DB)
    c = conn.cursor()
    c.execute('CREATE TABLE IF NOT EXISTS scraped_urls (url TEXT PRIMARY KEY)')
    conn.commit()
    conn.close()


def is_cached(url):
    """Check if a URL has already been scraped."""
    conn = sqlite3.connect(CACHE_DB)
    c = conn.cursor()
    c.execute('SELECT 1 FROM scraped_urls WHERE url=?', (url,))
    result = c.fetchone() is not None
    conn.close()
    return result


def cache_url(url):
    """Cache a URL as scraped."""
    conn = sqlite3.connect(CACHE_DB)
    c = conn.cursor()
    c.execute('INSERT OR IGNORE INTO scraped_urls (url) VALUES (?)', (url,))
    conn.commit()
    conn.close()

def scrape_selenium(keywords, locations, pages=1):
    """
    Scrape job listings using Selenium WebDriver.

    Args:
        keywords: List of job keywords to search for
        locations: List of locations to search in
        pages: Number of pages to scrape per keyword/location combination

    Returns:
        List of job dictionaries with title, company, location, and URL
    """
    init_db()
    jobs = []

    # Create Chrome driver with robust configuration
    driver = get_chrome_driver()
    if driver is None:
        LOGGER.error("Failed to create Chrome driver. Using fallback scraping method.")
        return scrape_jobs_fallback(keywords, locations, pages)

    wait = WebDriverWait(driver, 15)

    try:
        for kw in keywords:
            for loc in locations:
                url = f"https://www.linkedin.com/jobs/search/?keywords={kw}&location={loc}&f_TP=1%2C2&f_E=2%2C3"
                LOGGER.info(f"Scraping: {url}")

                try:
                    driver.get(url)
                    # Wait for page to load
                    wait.until(EC.presence_of_element_located((By.TAG_NAME, 'body')))
                except TimeoutException:
                    LOGGER.warning(f"Page load timeout for {kw} in {loc}")
                    continue
                except WebDriverException as e:
                    LOGGER.warning(f"WebDriver error loading page for {kw} in {loc}: {e}")
                    continue

                # Check if we can find job listings
                try:
                    wait.until(EC.presence_of_element_located((By.CLASS_NAME, 'jobs-search-results__list-item')))
                except TimeoutException:
                    LOGGER.warning(f"No job listings found for {kw} in {loc}")
                    continue
                except Exception as e:
                    LOGGER.warning(f"Failed to load job listings for {kw} in {loc}: {e}")
                    continue

                for page in range(pages):
                    LOGGER.info(f"Scraping page {page + 1} for {kw} in {loc}")

                    # Scroll to load more jobs
                    driver.execute_script('window.scrollTo(0, document.body.scrollHeight)')

                    try:
                        wait.until(EC.presence_of_all_elements_located((By.CLASS_NAME, 'jobs-search-results__list-item')))
                        items = driver.find_elements(By.CLASS_NAME, 'jobs-search-results__list-item')

                        for el in items:
                            try:
                                link = el.find_element(By.TAG_NAME, 'a').get_attribute('href')
                                if is_cached(link):
                                    continue

                                title = el.find_element(By.CSS_SELECTOR, 'h3').text
                                comp = el.find_element(By.CSS_SELECTOR, 'h4').text
                                loc_text = el.find_element(By.CSS_SELECTOR, '.job-search-card__location').text

                                jobs.append({
                                    'keyword': kw,
                                    'title': title,
                                    'company': comp,
                                    'location': loc_text,
                                    'url': link
                                })
                                cache_url(link)

                            except Exception as e:
                                LOGGER.warning(f"Failed to extract job data: {e}")
                                continue

                    except Exception as e:
                        LOGGER.warning(f"Failed to load job items on page {page + 1}: {e}")

                    # Try to go to next page
                    try:
                        nxt = driver.find_element(By.CLASS_NAME, 'artdeco-pagination__button--next')
                        if 'disabled' in nxt.get_attribute('class'):
                            LOGGER.info("Reached last page")
                            break
                        nxt.click()
                        wait.until(EC.staleness_of(nxt))
                    except Exception as e:
                        LOGGER.info(f"No more pages available: {e}")
                        break

    except Exception as e:
        LOGGER.error(f"Critical error during scraping: {e}")
        LOGGER.info("Attempting fallback scraping method due to critical error")
        try:
            driver.quit()
        except:
            pass
        return scrape_jobs_fallback(keywords, locations, pages)
    finally:
        try:
            driver.quit()
        except Exception as e:
            LOGGER.warning(f"Error closing driver: {e}")

    if len(jobs) == 0:
        LOGGER.warning("No jobs found with Selenium scraping, using fallback method")
        return scrape_jobs_fallback(keywords, locations, pages)

    LOGGER.info(f"Scraped {len(jobs)} jobs total")
    return jobs

def scrape_jobs(keywords, locations, pages=1):
    """
    Main scraping function that tries Selenium first, then falls back to mock data.

    Args:
        keywords: List of job keywords to search for
        locations: List of locations to search in
        pages: Number of pages to scrape per keyword/location combination

    Returns:
        List of job dictionaries with title, company, location, and URL
    """
    LOGGER.info(f"Starting job scrape for {keywords} in {locations}")

    # Try Selenium scraping first
    jobs = scrape_selenium(keywords, locations, pages)

    # If no jobs found, use fallback
    if not jobs:
        LOGGER.warning("No jobs found with Selenium, using fallback method")
        jobs = scrape_jobs_fallback(keywords, locations, pages)

    return jobs