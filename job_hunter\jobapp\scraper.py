import asyncio
import random
import sqlite3
import logging
import urllib.parse
import time
from datetime import datetime, timedelta
from playwright.async_api import async_playwright, TimeoutError
import requests
from bs4 import BeautifulSoup
import feedparser
from django.utils import timezone
from job_scraper.models import JobListing

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
LOGGER = logging.getLogger(__name__)

USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
]
CACHE_DB = 'jobs_cache.db'

def init_db():
    """Initialize SQLite database for caching scraped URLs."""
    try:
        conn = sqlite3.connect(CACHE_DB)
        c = conn.cursor()
        c.execute('CREATE TABLE IF NOT EXISTS scraped_urls (url TEXT PRIMARY KEY)')
        conn.commit()
    except sqlite3.Error as e:
        LOGGER.error(f"Database initialization failed: {e}")
    finally:
        conn.close()

def is_cached(url):
    """Check if a URL has already been scraped."""
    try:
        conn = sqlite3.connect(CACHE_DB)
        c = conn.cursor()
        c.execute('SELECT 1 FROM scraped_urls WHERE url=?', (url,))
        result = c.fetchone() is not None
        return result
    except sqlite3.Error as e:
        LOGGER.error(f"Database query failed: {e}")
        return False
    finally:
        conn.close()

def cache_url(url):
    """Cache a URL as scraped."""
    try:
        conn = sqlite3.connect(CACHE_DB)
        c = conn.cursor()
        c.execute('INSERT OR IGNORE INTO scraped_urls (url) VALUES (?)', (url,))
        conn.commit()
    except sqlite3.Error as e:
        LOGGER.error(f"Database cache failed: {e}")
    finally:
        conn.close()

async def scrape_playwright(url, source, keyword, location, pages=1, max_age_days=14):
    """Scrape job listings using Playwright."""
    jobs = []
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context(
            user_agent=random.choice(USER_AGENTS),
            viewport={'width': 1920, 'height': 1080}
        )
        page = await context.new_page()

        for attempt in range(3):
            try:
                await page.goto(url, wait_until="domcontentloaded", timeout=30000)
                await page.wait_for_selector('div[class*="card"], li[class*="job"], article', timeout=20000)

                for page_num in range(pages):
                    LOGGER.info(f"Scraping {source} page {page_num + 1} for {keyword} in {location}")
                    await page.evaluate('window.scrollTo(0, document.body.scrollHeight)')
                    await asyncio.sleep(random.uniform(1, 3))

                    # Source-specific selectors
                    if source == 'LinkedIn':
                        job_cards = await page.query_selector_all('div.base-card')
                        title_selector = 'h3.base-search-card__title'
                        company_selector = 'h4.base-search-card__subtitle'
                        location_selector = 'span.job-search-card__location'
                        link_selector = 'a.base-card__full-link'
                        date_selector = 'time.job-search-card__listdate'
                    elif source == 'Glassdoor':
                        job_cards = await page.query_selector_all('article[data-test="job-listing"]')
                        title_selector = 'a[data-test="job-link"]'
                        company_selector = 'div[data-test="employer-name"]'
                        location_selector = 'div[data-test="location"]'
                        link_selector = 'a[data-test="job-link"]'
                        date_selector = 'div[data-test="job-age"]'
                    elif source == 'Monster':
                        job_cards = await page.query_selector_all('div.job-card')
                        title_selector = 'h3.job-title'
                        company_selector = 'span.company'
                        location_selector = 'span.location'
                        link_selector = 'a.job-link'
                        date_selector = 'time.posted-date'
                    else:  # Fallback for other sources
                        job_cards = await page.query_selector_all('div[class*="card"], li[class*="job"], article')
                        title_selector = 'h3, h2, [class*="title"]'
                        company_selector = '[class*="company"], [class*="employer"]'
                        location_selector = '[class*="location"]'
                        link_selector = 'a'
                        date_selector = 'time, [class*="date"]'

                    for card in job_cards:
                        try:
                            link = await card.query_selector(link_selector)
                            link = await link.get_attribute('href') if link else None
                            if not link or is_cached(link):
                                continue

                            title = await card.query_selector(title_selector)
                            title = await title.inner_text() if title else 'N/A'

                            company = await card.query_selector(company_selector)
                            company = await company.inner_text() if company else 'N/A'

                            location_elem = await card.query_selector(location_selector)
                            location_text = await location_elem.inner_text() if location_elem else location

                            date_elem = await card.query_selector(date_selector)
                            post_date = None
                            if date_elem:
                                date_text = await date_elem.get_attribute('datetime') or await date_elem.inner_text()
                                try:
                                    if 'day' in date_text.lower():
                                        days_ago = int(''.join(filter(str.isdigit, date_text)))
                                        post_date = datetime.now() - timedelta(days=days_ago)
                                    else:
                                        post_date = datetime.strptime(date_text, '%Y-%m-%d')
                                    if (datetime.now() - post_date).days > max_age_days:
                                        continue
                                except ValueError:
                                    pass

                            job = {
                                'keyword': keyword,
                                'title': title.strip(),
                                'company': company.strip(),
                                'location': location_text.strip(),
                                'url': link,
                                'source': source,
                                'posted_date': post_date
                            }
                            jobs.append(job)
                            cache_url(link)
                        except Exception as e:
                            LOGGER.warning(f"Error extracting job from {source}: {e}")
                            continue

                    # Navigate to next page
                    next_button = await page.query_selector('button[aria-label*="Next"], a[class*="next"]')
                    if not next_button or await next_button.is_disabled():
                        LOGGER.info(f"No more pages for {source}, {keyword} in {location}")
                        break
                    await next_button.click()
                    await page.wait_for_load_state('domcontentloaded', timeout=20000)

                break
            except TimeoutError as e:
                LOGGER.warning(f"Timeout on {url}, attempt {attempt + 1}: {e}")
                if attempt == 2:
                    LOGGER.error(f"Failed after 3 attempts for {url}")
                    break
                await asyncio.sleep(random.uniform(2, 5))
            except Exception as e:
                LOGGER.warning(f"Error on {url}, attempt {attempt + 1}: {e}")
                if attempt == 2:
                    LOGGER.error(f"Failed after 3 attempts for {url}")
                    break
                await asyncio.sleep(random.uniform(2, 5))

        await browser.close()
    return jobs

def scrape_fallback(url, source, keyword, location, pages=1, max_age_days=14):
    """Fallback scraper using requests and BeautifulSoup."""
    LOGGER.info(f"Using fallback scraper for {source}")
    jobs = []
    headers = {'User-Agent': random.choice(USER_AGENTS)}

    for page in range(1, pages + 1):
        start = (page - 1) * 25
        paginated_url = f"{url}&start={start}" if source == 'LinkedIn' else url
        LOGGER.info(f"Scraping {source} fallback: {paginated_url}")

        try:
            response = requests.get(paginated_url, headers=headers, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')

            # Source-specific selectors
            if source == 'LinkedIn':
                job_cards = soup.select('div.base-card')
                title_selector = 'h3.base-search-card__title'
                company_selector = 'h4.base-search-card__subtitle'
                location_selector = 'span.job-search-card__location'
                link_selector = 'a.base-card__full-link'
            elif source == 'Glassdoor':
                job_cards = soup.select('article[data-test="job-listing"]')
                title_selector = 'a[data-test="job-link"]'
                company_selector = 'div[data-test="employer-name"]'
                location_selector = 'div[data-test="location"]'
                link_selector = 'a[data-test="job-link"]'
            elif source == 'Monster':
                job_cards = soup.select('div.job-card')
                title_selector = 'h3.job-title'
                company_selector = 'span.company'
                location_selector = 'span.location'
                link_selector = 'a.job-link'
            else:
                job_cards = soup.select('div[class*="card"], li[class*="job"], article')
                title_selector = 'h3, h2, [class*="title"]'
                company_selector = '[class*="company"], [class*="employer"]'
                location_selector = '[class*="location"]'
                link_selector = 'a'

            for card in job_cards:
                try:
                    link = card.select_one(link_selector)
                    link = link['href'] if link else None
                    if not link or is_cached(link):
                        continue

                    title = card.select_one(title_selector)
                    title = title.get_text(strip=True) if title else 'N/A'

                    company = card.select_one(company_selector)
                    company = company.get_text(strip=True) if company else 'N/A'

                    location_elem = card.select_one(location_selector)
                    location_text = location_elem.get_text(strip=True) if location_elem else location

                    jobs.append({
                        'keyword': keyword,
                        'title': title,
                        'company': company,
                        'location': location_text,
                        'url': link,
                        'source': source,
                        'posted_date': None  # Fallback can't reliably get date
                    })
                    cache_url(link)
                except Exception as e:
                    LOGGER.warning(f"Error parsing job card from {source}: {e}")
                    continue

            time.sleep(random.uniform(1, 3))
        except requests.RequestException as e:
            LOGGER.warning(f"Failed to fetch {paginated_url}: {e}")
            continue

    return jobs

def scrape_rss(url, source, keyword, location, max_age_days=14):
    """Scrape job listings from RSS feeds."""
    LOGGER.info(f"Scraping {source} RSS: {url}")
    jobs = []
    try:
        feed = feedparser.parse(url)
        for entry in feed.entries:
            link = entry.get('link', None)
            if not link or is_cached(link):
                continue
            title = entry.get('title', 'N/A')
            company = entry.get('author', 'N/A')
            post_date = entry.get('published', None)
            if post_date:
                try:
                    post_date = datetime.strptime(post_date, '%a, %d %b %Y %H:%M:%S %z')
                    if (datetime.now(post_date.tzinfo) - post_date).days > max_age_days:
                        continue
                except ValueError:
                    post_date = None
            jobs.append({
                'keyword': keyword,
                'title': title.strip(),
                'company': company.strip(),
                'location': location,
                'url': link,
                'source': source,
                'posted_date': post_date
            })
            cache_url(link)
    except Exception as e:
        LOGGER.warning(f"Error parsing RSS feed {url}: {e}")
    return jobs

async def scrape_jobs(keywords, locations, pages=1, max_age_days=14):
    """
    Scrape jobs from multiple sources and save to Django model.
    Args:
        keywords: List of job keywords
        locations: List of locations
        pages: Number of pages to scrape
        max_age_days: Max age of job postings in days
    Returns: Number of jobs saved
    """
    init_db()
    sources = [
        {
            'name': 'LinkedIn',
            'url_template': 'https://www.linkedin.com/jobs/search/?keywords={keyword}&location={location}&f_TPR=r1209600&f_E=2%2C3',
            'method': 'playwright'
        },
        {
            'name': 'Glassdoor',
            'url_template': 'https://www.glassdoor.com/Job/jobs.htm?keywords={keyword}&locT=C&locName={location}&fromAge=14',
            'method': 'playwright'
        },
        {
            'name': 'Monster',
            'url_template': 'https://www.monster.com/jobs/search?q={keyword}&where={location}&tm.w=2',
            'method': 'playwright'
        },
        {
            'name': 'Indeed',
            'url_template': 'https://www.indeed.com/rss?q={keyword}+{location}&f_TPR=r1209600',
            'method': 'rss'
        },
        {
            'name': 'SimplyHired',
            'url_template': 'https://www.simplyhired.com/search?q={keyword}&l={location}&fdr=14',
            'method': 'playwright'
        },
        {
            'name': 'CareerBuilder',
            'url_template': 'https://www.careerbuilder.com/jobs?keywords={keyword}&location={location}&posted_date=14',
            'method': 'playwright'
        },
        {
            'name': 'JobStreet',
            'url_template': 'https://www.jobstreet.com/jobs?keywords={keyword}&location={location}&posted_within=14',
            'method': 'playwright'
        }
    ]

    jobs_saved = 0
    for source in sources:
        for keyword in keywords:
            for location in locations:
                encoded_keyword = urllib.parse.quote(keyword)
                encoded_location = urllib.parse.quote(location)
                url = source['url_template'].format(keyword=encoded_keyword, location=encoded_location)
                LOGGER.info(f"Processing {source['name']} for {keyword} in {location}")

                if source['method'] == 'playwright':
                    jobs = await scrape_playwright(url, source['name'], keyword, location, pages, max_age_days)
                    if not jobs:
                        LOGGER.warning(f"No jobs found with Playwright for {source['name']}, using fallback")
                        jobs = scrape_fallback(url, source['name'], keyword, location, pages, max_age_days)
                else:  # RSS
                    jobs = scrape_rss(url, source['name'], keyword, location, max_age_days)

                for job in jobs:
                    try:
                        JobListing.objects.create(
                            keyword=job['keyword'],
                            title=job['title'],
                            company=job['company'],
                            location=job['location'],
                            url=job['url'],
                            source=job['source'],
                            posted_date=job['posted_date'] if job['posted_date'] else None
                        )
                        jobs_saved += 1
                    except Exception as e:
                        LOGGER.warning(f"Error saving job to database: {e}")
                        continue

                time.sleep(random.uniform(1, 3))  # Avoid rate limiting

    LOGGER.info(f"Saved {jobs_saved} jobs to database")
    return jobs_saved