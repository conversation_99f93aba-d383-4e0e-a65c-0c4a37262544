{% extends 'jobapp/base.html' %}

{% block title %}Search Results - Job Hunter{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2>
                    <i class="fas fa-search"></i> Search Results
                </h2>
                <p class="text-muted mb-0">
                    Found {{ total_found }} jobs for "{{ keywords }}" in "{{ locations }}"
                    {% if showing_count != total_found %}
                        (showing top {{ showing_count }})
                    {% endif %}
                </p>
            </div>
            <div>
                <a href="{% url 'jobapp:job_search' %}" class="btn btn-outline-primary">
                    <i class="fas fa-search"></i> New Search
                </a>
            </div>
        </div>
    </div>
</div>

{% if jobs %}
<div class="row">
    {% for job in jobs %}
    <div class="col-lg-6 mb-4">
        <div class="card job-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h5 class="card-title mb-0">
                        <a href="{% if job.job_obj %}{% url 'jobapp:job_detail' job.job_obj.id %}{% else %}{{ job.url }}{% endif %}" 
                           class="text-decoration-none" target="_blank">
                            {{ job.title }}
                        </a>
                    </h5>
                    {% if job.similarity %}
                    <span class="similarity-score">
                        {{ job.similarity|floatformat:1 }}% match
                    </span>
                    {% endif %}
                </div>
                
                <h6 class="card-subtitle mb-2 text-muted">
                    <i class="fas fa-building"></i> {{ job.company }}
                </h6>
                
                <p class="card-text">
                    <i class="fas fa-map-marker-alt"></i> {{ job.location }}
                </p>
                
                {% if job.keywords %}
                <div class="mb-3">
                    <small class="text-muted">Keywords:</small><br>
                    {% for keyword in job.keywords|slice:":5" %}
                        <span class="badge bg-light text-dark me-1">{{ keyword }}</span>
                    {% endfor %}
                    {% if job.keywords|length > 5 %}
                        <span class="text-muted">+{{ job.keywords|length|add:"-5" }} more</span>
                    {% endif %}
                </div>
                {% endif %}
                
                <div class="d-flex justify-content-between align-items-center">
                    <a href="{{ job.url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-external-link-alt"></i> View Job
                    </a>
                    
                    {% if user.is_authenticated %}
                    <div class="btn-group" role="group">
                        {% if job.job_obj %}
                        <a href="{% url 'jobapp:apply_job' job.job_obj.id %}" class="btn btn-success btn-sm">
                            <i class="fas fa-plus"></i> Save
                        </a>
                        {% endif %}
                        
                        {% if user.resumes.exists and job.job_obj %}
                        <div class="dropdown">
                            <button class="btn btn-info btn-sm dropdown-toggle" type="button" 
                                    data-bs-toggle="dropdown">
                                <i class="fas fa-file-alt"></i> Cover Letter
                            </button>
                            <ul class="dropdown-menu">
                                {% for resume in user.resumes.all %}
                                <li>
                                    <a class="dropdown-item" 
                                       href="{% url 'jobapp:generate_cover_letter' job.job_obj.id resume.id %}">
                                        {{ resume.title }}
                                    </a>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<div class="row mt-4">
    <div class="col-12 text-center">
        <div class="card bg-light">
            <div class="card-body">
                <h5>Want to refine your search?</h5>
                <p class="mb-3">Try different keywords or locations to find more opportunities.</p>
                <a href="{% url 'jobapp:job_search' %}" class="btn btn-primary">
                    <i class="fas fa-search"></i> Search Again
                </a>
                {% if user.is_authenticated %}
                <a href="{% url 'jobapp:application_list' %}" class="btn btn-outline-success ms-2">
                    <i class="fas fa-clipboard-list"></i> View My Applications
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="row">
    <div class="col-lg-8 mx-auto text-center">
        <div class="card">
            <div class="card-body">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4>No jobs found</h4>
                <p class="text-muted">
                    We couldn't find any jobs matching your search criteria. 
                    Try adjusting your keywords or locations.
                </p>
                <a href="{% url 'jobapp:job_search' %}" class="btn btn-primary">
                    <i class="fas fa-search"></i> Try Another Search
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% if not user.is_authenticated %}
<div class="row mt-4">
    <div class="col-lg-8 mx-auto">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <strong>Tip:</strong> Login to save jobs, track applications, and generate personalized cover letters.
            <a href="{% url 'jobapp:login' %}" class="btn btn-sm btn-primary ms-2">Login</a>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
