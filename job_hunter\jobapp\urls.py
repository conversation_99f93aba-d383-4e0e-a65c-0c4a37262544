from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

app_name = 'jobapp'

urlpatterns = [
    # Authentication
    path('login/', auth_views.LoginView.as_view(template_name='jobapp/login.html'), name='login'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),
    path('register/', views.register, name='register'),

    # Main pages
    path('', views.home, name='home'),
    path('search/', views.job_search, name='job_search'),
    path('results/', views.search_results, name='search_results'),
    
    # Job management
    path('job/<int:job_id>/', views.job_detail, name='job_detail'),
    path('job/<int:job_id>/apply/', views.apply_job, name='apply_job'),
    path('scrape/', views.scrape_view, name='scrape_jobs'),
    
    # Resume management
    path('resumes/', views.resume_list, name='resume_list'),
    path('resumes/upload/', views.resume_upload, name='resume_upload'),
    path('resumes/<int:resume_id>/', views.resume_detail, name='resume_detail'),
    path('resumes/<int:resume_id>/edit/', views.resume_edit, name='resume_edit'),
    path('resumes/<int:resume_id>/delete/', views.resume_delete, name='resume_delete'),
    
    # Applications
    path('applications/', views.application_list, name='application_list'),
    path('applications/<int:app_id>/', views.application_detail, name='application_detail'),
    path('applications/<int:app_id>/update/', views.application_update, name='application_update'),
    
    # Cover letter generation
    path('generate-cover-letter/<int:job_id>/<int:resume_id>/', views.generate_cover_letter, name='generate_cover_letter'),
    
    # User profile
    path('profile/', views.user_profile, name='user_profile'),
    path('profile/edit/', views.edit_profile, name='edit_profile'),
    
    # API endpoints
    path('api/match-jobs/', views.api_match_jobs, name='api_match_jobs'),
    path('api/job-stats/', views.api_job_stats, name='api_job_stats'),
]
