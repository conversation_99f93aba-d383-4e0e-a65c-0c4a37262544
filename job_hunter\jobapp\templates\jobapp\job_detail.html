{% extends 'jobapp/base.html' %}
{% load static %}

{% block title %}{{ job.title }} at {{ job.company }} - <PERSON>{% endblock %}

{% block extra_css %}
<style>
    .job-detail-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .job-header {
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .job-title {
        color: #1f2937;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .job-company {
        color: #6366f1;
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .job-location {
        color: #6b7280;
        font-size: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .job-meta {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .meta-item {
        background: #f9fafb;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #6366f1;
    }
    
    .meta-label {
        font-weight: 600;
        color: #374151;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .meta-value {
        color: #1f2937;
        font-size: 1rem;
        margin-top: 0.25rem;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        margin-top: 2rem;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
        color: white;
        text-decoration: none;
    }
    
    .btn-secondary {
        background: white;
        color: #6366f1;
        padding: 0.75rem 1.5rem;
        border: 2px solid #6366f1;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        background: #6366f1;
        color: white;
        text-decoration: none;
    }
    
    .job-description {
        background: #f9fafb;
        padding: 1.5rem;
        border-radius: 8px;
        margin-top: 2rem;
    }
    
    .section-title {
        color: #1f2937;
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .back-link {
        color: #6366f1;
        text-decoration: none;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .back-link:hover {
        color: #4f46e5;
        text-decoration: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <a href="{% url 'jobapp:search_results' %}" class="back-link">
        <i class="fas fa-arrow-left"></i>
        Back to Search Results
    </a>
    
    <div class="job-detail-card">
        <div class="job-header">
            <h1 class="job-title">{{ job.title }}</h1>
            <h2 class="job-company">{{ job.company }}</h2>
            <div class="job-location">
                <i class="fas fa-map-marker-alt"></i>
                {{ job.location }}
            </div>
        </div>
        
        <div class="job-meta">
            {% if job.keyword %}
            <div class="meta-item">
                <div class="meta-label">Search Keyword</div>
                <div class="meta-value">{{ job.keyword }}</div>
            </div>
            {% endif %}
            
            {% if job.url %}
            <div class="meta-item">
                <div class="meta-label">Source</div>
                <div class="meta-value">
                    <a href="{{ job.url }}" target="_blank" class="text-primary">
                        View Original Posting <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>
            </div>
            {% endif %}
            
            <div class="meta-item">
                <div class="meta-label">Job ID</div>
                <div class="meta-value">#{{ job.id }}</div>
            </div>
            
            {% if job.created_at %}
            <div class="meta-item">
                <div class="meta-label">Added to System</div>
                <div class="meta-value">{{ job.created_at|date:"M d, Y" }}</div>
            </div>
            {% endif %}
        </div>
        
        {% if job.description %}
        <div class="job-description">
            <h3 class="section-title">Job Description</h3>
            <div>{{ job.description|linebreaks }}</div>
        </div>
        {% else %}
        <div class="job-description">
            <h3 class="section-title">Job Description</h3>
            <p class="text-muted">
                No description available. Please visit the original job posting for more details.
            </p>
        </div>
        {% endif %}
        
        <div class="action-buttons">
            {% if job.url %}
            <a href="{{ job.url }}" target="_blank" class="btn-primary">
                <i class="fas fa-external-link-alt"></i>
                Apply on Original Site
            </a>
            {% endif %}
            
            {% if user.is_authenticated %}
            <a href="{% url 'jobapp:apply_job' job.id %}" class="btn-secondary">
                <i class="fas fa-file-alt"></i>
                Track Application
            </a>
            {% endif %}
            
            <button onclick="window.print()" class="btn-secondary">
                <i class="fas fa-print"></i>
                Print Job Details
            </button>
        </div>
    </div>
    
    {% if user.is_authenticated %}
    <div class="job-detail-card">
        <h3 class="section-title">Quick Actions</h3>
        <div class="row">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-file-alt text-primary"></i>
                            Generate Cover Letter
                        </h5>
                        <p class="card-text">
                            Create a personalized cover letter for this position using AI.
                        </p>
                        <a href="#" class="btn btn-outline-primary" onclick="alert('Please select a resume first')">
                            Generate Cover Letter
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-chart-line text-success"></i>
                            Job Match Analysis
                        </h5>
                        <p class="card-text">
                            See how well this job matches your resume and skills.
                        </p>
                        <a href="#" class="btn btn-outline-success" onclick="alert('Job analysis feature coming soon!')">
                            Analyze Match
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add any job-specific JavaScript here
    document.addEventListener('DOMContentLoaded', function() {
        // Track job view analytics
        console.log('Job viewed:', '{{ job.title }}', 'at', '{{ job.company }}');
    });
</script>
{% endblock %}
