{% extends 'jobapp/base.html' %}

{% block title %}{{ resume.title }} - <PERSON>{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2>
                <i class="fas fa-file-alt"></i> {{ resume.title }}
            </h2>
            <div class="btn-group" role="group">
                <a href="{% url 'jobapp:resume_edit' resume.id %}" class="btn btn-outline-primary">
                    <i class="fas fa-edit"></i> Edit
                </a>
                <a href="{% url 'jobapp:resume_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Resumes
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Resume Content</h5>
            </div>
            <div class="card-body">
                {% if resume.content %}
                    <pre class="bg-light p-3 rounded">{{ resume.content }}</pre>
                {% else %}
                    <p class="text-muted">No text content available.</p>
                {% endif %}
                
                {% if resume.file %}
                    <div class="mt-3">
                        <a href="{{ resume.file.url }}" class="btn btn-outline-primary" target="_blank">
                            <i class="fas fa-download"></i> Download File
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Resume Details</h5>
            </div>
            <div class="card-body">
                <p><strong>Status:</strong> 
                    {% if resume.is_active %}
                        <span class="badge bg-success">Active</span>
                    {% else %}
                        <span class="badge bg-secondary">Inactive</span>
                    {% endif %}
                </p>
                <p><strong>Created:</strong> {{ resume.created_at|date:"M d, Y H:i" }}</p>
                <p><strong>Updated:</strong> {{ resume.updated_at|date:"M d, Y H:i" }}</p>
                {% if resume.file %}
                    <p><strong>File:</strong> {{ resume.file.name|default:"No file" }}</p>
                {% endif %}
            </div>
        </div>
        
        {% if applications %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">Applications Using This Resume</h5>
            </div>
            <div class="card-body">
                {% for app in applications %}
                <div class="mb-2">
                    <a href="{% url 'jobapp:application_detail' app.id %}" class="text-decoration-none">
                        <strong>{{ app.job.title }}</strong>
                    </a><br>
                    <small class="text-muted">{{ app.job.company }} - {{ app.get_status_display }}</small>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
