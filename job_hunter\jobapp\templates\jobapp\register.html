{% extends 'jobapp/base.html' %}

{% block title %}Register - Job Hunter{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card">
            <div class="card-header text-center">
                <h4 class="mb-0">
                    <i class="fas fa-user-plus"></i> Create Account
                </h4>
            </div>
            <div class="card-body">
                {% if form.errors %}
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            <i class="fas fa-user"></i> Username
                        </label>
                        <input type="text" 
                               class="form-control" 
                               id="{{ form.username.id_for_label }}" 
                               name="{{ form.username.name }}" 
                               value="{{ form.username.value|default:'' }}"
                               required>
                        {% if form.username.help_text %}
                            <div class="form-text">{{ form.username.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.password1.id_for_label }}" class="form-label">
                            <i class="fas fa-lock"></i> Password
                        </label>
                        <input type="password" 
                               class="form-control" 
                               id="{{ form.password1.id_for_label }}" 
                               name="{{ form.password1.name }}"
                               required>
                        {% if form.password1.help_text %}
                            <div class="form-text">{{ form.password1.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.password2.id_for_label }}" class="form-label">
                            <i class="fas fa-lock"></i> Confirm Password
                        </label>
                        <input type="password" 
                               class="form-control" 
                               id="{{ form.password2.id_for_label }}" 
                               name="{{ form.password2.name }}"
                               required>
                        {% if form.password2.help_text %}
                            <div class="form-text">{{ form.password2.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i> Create Account
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <p class="mb-2">
                        <small class="text-muted">Already have an account?</small>
                    </p>
                    <a href="{% url 'jobapp:login' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
