{% extends 'jobapp/base.html' %}

{% block title %}Home - Job Hunter{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto text-center">
        <h1 class="display-4 mb-4">
            <i class="fas fa-rocket text-primary"></i> 
            Welcome to Job Hunter
        </h1>
        <p class="lead mb-5">
            Your AI-powered job search companion. Find the perfect job matches, 
            generate personalized cover letters, and track your applications all in one place.
        </p>
    </div>
</div>

<div class="row mb-5">
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="fas fa-search fa-3x text-primary mb-3"></i>
                <h5 class="card-title">Smart Job Search</h5>
                <p class="card-text">
                    Search and scrape job listings from LinkedIn with advanced filtering options.
                </p>
                <a href="{% url 'jobapp:job_search' %}" class="btn btn-primary">
                    Start Searching
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="fas fa-brain fa-3x text-success mb-3"></i>
                <h5 class="card-title">AI Matching</h5>
                <p class="card-text">
                    Get personalized job recommendations based on your resume and preferences.
                </p>
                {% if user.is_authenticated %}
                    <a href="{% url 'jobapp:resume_list' %}" class="btn btn-success">
                        Manage Resumes
                    </a>
                {% else %}
                    <a href="{% url 'jobapp:login' %}" class="btn btn-success">
                        Login to Start
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="fas fa-file-alt fa-3x text-info mb-3"></i>
                <h5 class="card-title">Cover Letters</h5>
                <p class="card-text">
                    Generate personalized cover letters using AI for each job application.
                </p>
                {% if user.is_authenticated %}
                    <a href="{% url 'jobapp:application_list' %}" class="btn btn-info">
                        View Applications
                    </a>
                {% else %}
                    <a href="{% url 'jobapp:login' %}" class="btn btn-info">
                        Login to Start
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% if user.is_authenticated %}
<div class="row">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line"></i> Quick Stats
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h3 class="text-primary">{{ stats.total_applications|default:0 }}</h3>
                        <small class="text-muted">Applications</small>
                    </div>
                    <div class="col-4">
                        <h3 class="text-success">{{ stats.total_resumes|default:0 }}</h3>
                        <small class="text-muted">Resumes</small>
                    </div>
                    <div class="col-4">
                        <h3 class="text-info">{{ stats.recent_searches|default:0 }}</h3>
                        <small class="text-muted">Recent Searches</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock"></i> Recent Activity
                </h5>
            </div>
            <div class="card-body">
                {% if recent_applications %}
                    {% for app in recent_applications %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong>{{ app.job.title }}</strong><br>
                            <small class="text-muted">{{ app.job.company }}</small>
                        </div>
                        <span class="badge bg-{{ app.status|default:'secondary' }}">
                            {{ app.get_status_display }}
                        </span>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted mb-0">No recent applications. Start by searching for jobs!</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-lg-8 mx-auto text-center">
        <div class="card bg-light">
            <div class="card-body">
                <h5 class="card-title">Ready to Get Started?</h5>
                <p class="card-text">
                    Create an account or login to access all features including resume management, 
                    job tracking, and personalized recommendations.
                </p>
                <a href="{% url 'jobapp:login' %}" class="btn btn-primary btn-lg me-2">
                    <i class="fas fa-sign-in-alt"></i> Login
                </a>
                <a href="{% url 'jobapp:register' %}" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-user-plus"></i> Register
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
