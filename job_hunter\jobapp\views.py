from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth import login
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils import timezone
from django.conf import settings
import csv
import json
import logging
from io import StringIO

from .models import Job, Resume, JobApplication, UserProfile, SearchHistory
from .scraper import scrape_jobs
from .pipeline import run_pipeline
from .match import match_jobs_to_resume
from .analyzer import analyze_jobs
from .generator import generate_cover_letter as gen_cover_letter

logger = logging.getLogger(__name__)


def register(request):
    """User registration view."""
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            messages.success(request, 'Account created successfully! Welcome to Job Hunter.')
            return redirect('jobapp:home')
    else:
        form = UserCreationForm()

    return render(request, 'jobapp/register.html', {'form': form})


def home(request):
    """Home page view with user stats and recent activity."""
    context = {}

    if request.user.is_authenticated:
        # Get user stats
        context['stats'] = {
            'total_applications': JobApplication.objects.filter(user=request.user).count(),
            'total_resumes': Resume.objects.filter(user=request.user).count(),
            'recent_searches': SearchHistory.objects.filter(user=request.user).count(),
        }

        # Get recent applications
        context['recent_applications'] = JobApplication.objects.filter(
            user=request.user
        ).select_related('job').order_by('-updated_at')[:5]

    return render(request, 'jobapp/home.html', context)


def job_search(request):
    """Job search form page."""
    context = {}

    if request.user.is_authenticated:
        # Get recent searches
        context['recent_searches'] = SearchHistory.objects.filter(
            user=request.user
        ).order_by('-created_at')[:10]

    return render(request, 'jobapp/job_search.html', context)


def search_results(request):
    """Handle job search and display results."""
    if request.method != 'POST':
        return redirect('jobapp:job_search')

    keywords = request.POST.get('keywords', '').strip()
    locations = request.POST.get('locations', '').strip()
    pages = int(request.POST.get('pages', 2))
    resume_id = request.POST.get('resume_id')
    top_k = int(request.POST.get('top_k', 10))

    if not keywords or not locations:
        messages.error(request, 'Please provide both keywords and locations.')
        return redirect('jobapp:job_search')

    # Parse comma-separated values
    keyword_list = [k.strip() for k in keywords.split(',') if k.strip()]
    location_list = [l.strip() for l in locations.split(',') if l.strip()]

    try:
        # Scrape jobs
        logger.info(f"Starting job scrape for {keyword_list} in {location_list}")
        scraped_jobs = scrape_jobs(keyword_list, location_list, pages)

        # Save jobs to database
        saved_jobs = []
        for job_data in scraped_jobs:
            job, created = Job.objects.get_or_create(
                url=job_data['url'],
                defaults={
                    'title': job_data['title'],
                    'company': job_data['company'],
                    'location': job_data['location'],
                }
            )
            saved_jobs.append(job)

        # Analyze jobs
        job_dicts = [
            {
                'id': job.id,
                'title': job.title,
                'company': job.company,
                'location': job.location,
                'description': job.description,
                'url': job.url
            }
            for job in saved_jobs
        ]

        analyzed_jobs = analyze_jobs(job_dicts)

        # Update jobs with keywords
        for job_dict in analyzed_jobs:
            job = Job.objects.get(id=job_dict['id'])
            job.keywords = job_dict.get('keywords', [])
            job.save()

        # Match with resume if provided
        if resume_id and request.user.is_authenticated:
            try:
                resume = Resume.objects.get(id=resume_id, user=request.user)
                matched_jobs = match_jobs_to_resume(analyzed_jobs, resume.content, top_k)

                # Update similarity scores
                for job_dict in matched_jobs:
                    job = Job.objects.get(id=job_dict['id'])
                    job_dict['job_obj'] = job

                jobs = matched_jobs
            except Resume.DoesNotExist:
                jobs = analyzed_jobs[:top_k]
                messages.warning(request, 'Resume not found. Showing all results.')
        else:
            jobs = analyzed_jobs[:top_k]

        # Save search history
        if request.user.is_authenticated:
            SearchHistory.objects.create(
                user=request.user,
                keywords=keywords,
                locations=locations,
                results_count=len(jobs)
            )

        messages.success(request, f'Found {len(scraped_jobs)} jobs, showing top {len(jobs)} results.')

    except Exception as e:
        logger.error(f"Error during job search: {e}")
        messages.error(request, f'Error occurred during job search: {str(e)}')
        return redirect('jobapp:job_search')

    context = {
        'jobs': jobs,
        'keywords': keywords,
        'locations': locations,
        'total_found': len(scraped_jobs),
        'showing_count': len(jobs),
    }

    return render(request, 'jobapp/search_results.html', context)


@csrf_exempt
def scrape_view(request):
    """Legacy scrape view for CSV export."""
    if request.method == 'POST':
        keywords = request.POST.get('keywords', '').split(',')
        locations = request.POST.get('locations', '').split(',')
        pages = int(request.POST.get('pages', 2))

        try:
            jobs = scrape_jobs(keywords, locations, pages)
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename="jobs.csv"'
            writer = csv.DictWriter(response, fieldnames=['keyword', 'title', 'company', 'location', 'url'])
            writer.writeheader()
            writer.writerows(jobs)
            return response
        except Exception as e:
            logger.error(f"Error in scrape_view: {e}")
            return HttpResponse(f"Error: {str(e)}", status=500)

    return render(request, 'jobapp/job_search.html')


def job_detail(request, job_id):
    """Display detailed view of a job."""
    job = get_object_or_404(Job, id=job_id)

    context = {'job': job}

    if request.user.is_authenticated:
        # Check if user has applied
        try:
            application = JobApplication.objects.get(user=request.user, job=job)
            context['application'] = application
        except JobApplication.DoesNotExist:
            context['application'] = None

        # Get user's resumes for cover letter generation
        context['resumes'] = Resume.objects.filter(user=request.user, is_active=True)

    return render(request, 'jobapp/job_detail.html', context)


@login_required
def apply_job(request, job_id):
    """Apply for a job."""
    job = get_object_or_404(Job, id=job_id)

    if request.method == 'POST':
        resume_id = request.POST.get('resume_id')
        notes = request.POST.get('notes', '')

        if not resume_id:
            messages.error(request, 'Please select a resume.')
            return redirect('jobapp:job_detail', job_id=job_id)

        try:
            resume = Resume.objects.get(id=resume_id, user=request.user)

            # Create or update application
            application, created = JobApplication.objects.get_or_create(
                user=request.user,
                job=job,
                defaults={
                    'resume': resume,
                    'status': 'saved',
                    'notes': notes
                }
            )

            if not created:
                application.resume = resume
                application.notes = notes
                application.save()
                messages.info(request, 'Application updated successfully.')
            else:
                messages.success(request, 'Job saved to your applications.')

            return redirect('jobapp:application_detail', app_id=application.id)

        except Resume.DoesNotExist:
            messages.error(request, 'Resume not found.')
            return redirect('jobapp:job_detail', job_id=job_id)

    return redirect('jobapp:job_detail', job_id=job_id)


@login_required
def resume_list(request):
    """List user's resumes."""
    resumes = Resume.objects.filter(user=request.user).order_by('-updated_at')

    context = {
        'resumes': resumes
    }

    return render(request, 'jobapp/resume_list.html', context)


@login_required
def resume_upload(request):
    """Upload a new resume."""
    if request.method == 'POST':
        title = request.POST.get('title', '').strip()
        content = request.POST.get('content', '').strip()
        file = request.FILES.get('file')

        if not title:
            messages.error(request, 'Please provide a title for your resume.')
            return render(request, 'jobapp/resume_upload.html')

        if not content and not file:
            messages.error(request, 'Please provide either resume content or upload a file.')
            return render(request, 'jobapp/resume_upload.html')

        # Create resume
        resume = Resume.objects.create(
            user=request.user,
            title=title,
            content=content,
            file=file
        )

        messages.success(request, 'Resume uploaded successfully.')
        return redirect('jobapp:resume_detail', resume_id=resume.id)

    return render(request, 'jobapp/resume_upload.html')


@login_required
def resume_detail(request, resume_id):
    """Display resume details."""
    resume = get_object_or_404(Resume, id=resume_id, user=request.user)

    # Get applications using this resume
    applications = JobApplication.objects.filter(resume=resume).select_related('job')

    context = {
        'resume': resume,
        'applications': applications
    }

    return render(request, 'jobapp/resume_detail.html', context)


@login_required
def resume_edit(request, resume_id):
    """Edit a resume."""
    resume = get_object_or_404(Resume, id=resume_id, user=request.user)

    if request.method == 'POST':
        title = request.POST.get('title', '').strip()
        content = request.POST.get('content', '').strip()
        file = request.FILES.get('file')

        if not title:
            messages.error(request, 'Please provide a title for your resume.')
            return render(request, 'jobapp/resume_edit.html', {'resume': resume})

        resume.title = title
        resume.content = content
        if file:
            resume.file = file
        resume.save()

        messages.success(request, 'Resume updated successfully.')
        return redirect('jobapp:resume_detail', resume_id=resume.id)

    return render(request, 'jobapp/resume_edit.html', {'resume': resume})


@login_required
def resume_delete(request, resume_id):
    """Delete a resume."""
    resume = get_object_or_404(Resume, id=resume_id, user=request.user)

    if request.method == 'POST':
        resume.delete()
        messages.success(request, 'Resume deleted successfully.')
        return redirect('jobapp:resume_list')

    return render(request, 'jobapp/resume_confirm_delete.html', {'resume': resume})


@login_required
def application_list(request):
    """List user's job applications."""
    applications = JobApplication.objects.filter(user=request.user).select_related('job', 'resume')

    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        applications = applications.filter(status=status_filter)

    # Pagination
    paginator = Paginator(applications, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'status_choices': JobApplication.STATUS_CHOICES,
        'current_status': status_filter
    }

    return render(request, 'jobapp/application_list.html', context)


@login_required
def application_detail(request, app_id):
    """Display application details."""
    application = get_object_or_404(JobApplication, id=app_id, user=request.user)

    context = {
        'application': application
    }

    return render(request, 'jobapp/application_detail.html', context)


@login_required
def application_update(request, app_id):
    """Update application status."""
    application = get_object_or_404(JobApplication, id=app_id, user=request.user)

    if request.method == 'POST':
        status = request.POST.get('status')
        notes = request.POST.get('notes', '')

        if status in dict(JobApplication.STATUS_CHOICES):
            application.status = status
            application.notes = notes
            application.save()
            messages.success(request, 'Application updated successfully.')
        else:
            messages.error(request, 'Invalid status.')

    return redirect('jobapp:application_detail', app_id=app_id)


@login_required
def generate_cover_letter(request, job_id, resume_id):
    """Generate a cover letter for a job application."""
    job = get_object_or_404(Job, id=job_id)
    resume = get_object_or_404(Resume, id=resume_id, user=request.user)

    try:
        # Prepare job data for cover letter generation
        job_data = {
            'title': job.title,
            'company': job.company,
            'location': job.location,
            'description': job.description
        }

        cover_letter = gen_cover_letter(job_data, resume.content)

        # Save or update application with cover letter
        application, created = JobApplication.objects.get_or_create(
            user=request.user,
            job=job,
            defaults={
                'resume': resume,
                'cover_letter': cover_letter,
                'status': 'saved'
            }
        )

        if not created:
            application.cover_letter = cover_letter
            application.save()

        messages.success(request, 'Cover letter generated successfully.')
        return redirect('jobapp:application_detail', app_id=application.id)

    except Exception as e:
        logger.error(f"Error generating cover letter: {e}")
        messages.error(request, f'Error generating cover letter: {str(e)}')
        return redirect('jobapp:job_detail', job_id=job_id)


@login_required
def user_profile(request):
    """Display user profile."""
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    context = {
        'profile': profile
    }

    return render(request, 'jobapp/user_profile.html', context)


@login_required
def edit_profile(request):
    """Edit user profile."""
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        # Update user fields
        request.user.first_name = request.POST.get('first_name', '')
        request.user.last_name = request.POST.get('last_name', '')
        request.user.email = request.POST.get('email', '')
        request.user.save()

        # Update profile fields
        profile.phone = request.POST.get('phone', '')
        profile.linkedin_profile = request.POST.get('linkedin_profile', '')
        profile.github_profile = request.POST.get('github_profile', '')
        profile.portfolio_website = request.POST.get('portfolio_website', '')
        profile.preferred_locations = request.POST.get('preferred_locations', '')
        profile.preferred_keywords = request.POST.get('preferred_keywords', '')
        profile.save()

        messages.success(request, 'Profile updated successfully.')
        return redirect('jobapp:user_profile')

    context = {
        'profile': profile
    }

    return render(request, 'jobapp/edit_profile.html', context)


# API Views
@login_required
def api_match_jobs(request):
    """API endpoint to match jobs with a resume."""
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        data = json.loads(request.body)
        resume_id = data.get('resume_id')
        job_ids = data.get('job_ids', [])
        top_k = data.get('top_k', 10)

        if not resume_id:
            return JsonResponse({'error': 'resume_id required'}, status=400)

        resume = Resume.objects.get(id=resume_id, user=request.user)
        jobs = Job.objects.filter(id__in=job_ids)

        # Convert to format expected by matching function
        job_dicts = [
            {
                'id': job.id,
                'title': job.title,
                'company': job.company,
                'location': job.location,
                'description': job.description,
                'url': job.url
            }
            for job in jobs
        ]

        matched_jobs = match_jobs_to_resume(job_dicts, resume.content, top_k)

        return JsonResponse({
            'success': True,
            'matched_jobs': matched_jobs
        })

    except Resume.DoesNotExist:
        return JsonResponse({'error': 'Resume not found'}, status=404)
    except Exception as e:
        logger.error(f"Error in api_match_jobs: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def api_job_stats(request):
    """API endpoint to get job statistics."""
    try:
        stats = {
            'total_jobs': Job.objects.count(),
            'user_applications': JobApplication.objects.filter(user=request.user).count(),
            'user_resumes': Resume.objects.filter(user=request.user).count(),
            'recent_jobs': Job.objects.filter(scraped_at__gte=timezone.now() - timezone.timedelta(days=7)).count(),
            'application_stats': {
                status: JobApplication.objects.filter(user=request.user, status=status).count()
                for status, _ in JobApplication.STATUS_CHOICES
            }
        }

        return JsonResponse({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        logger.error(f"Error in api_job_stats: {e}")
        return JsonResponse({'error': str(e)}, status=500)
