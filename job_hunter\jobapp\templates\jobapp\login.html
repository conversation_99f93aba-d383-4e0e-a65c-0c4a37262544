{% extends 'jobapp/base.html' %}

{% block title %}Login - <PERSON> Hunter{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-header text-center">
                <h4 class="mb-0">
                    <i class="fas fa-sign-in-alt"></i> Login to <PERSON> Hunter
                </h4>
            </div>
            <div class="card-body">
                {% if form.errors %}
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            <i class="fas fa-user"></i> Username
                        </label>
                        <input type="text" 
                               class="form-control" 
                               id="{{ form.username.id_for_label }}" 
                               name="{{ form.username.name }}" 
                               value="{{ form.username.value|default:'' }}"
                               required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.password.id_for_label }}" class="form-label">
                            <i class="fas fa-lock"></i> Password
                        </label>
                        <input type="password" 
                               class="form-control" 
                               id="{{ form.password.id_for_label }}" 
                               name="{{ form.password.name }}"
                               required>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <p class="mb-2">
                        <small class="text-muted">Don't have an account?</small>
                    </p>
                    <a href="{% url 'jobapp:register' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-user-plus"></i> Register
                    </a>
                </div>
                
                <div class="mt-3">
                    <div class="alert alert-info">
                        <strong>Demo Account:</strong><br>
                        Username: <code>demo</code><br>
                        Password: <code>demo123</code>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
