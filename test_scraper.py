#!/usr/bin/env python3
"""
Test script for the job scraper
"""

import os
import sys
import django

# Add the job_hunter directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'job_hunter'))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'job_hunter.settings')
django.setup()

# Now import and test the scraper
from jobapp.scraper import scrape_jobs
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_scraper():
    print("Testing job scraper...")
    print("=" * 50)
    
    try:
        # Test with a simple search
        keywords = ['Python Developer']
        locations = ['Dubai']
        pages = 1
        
        print(f"Searching for: {keywords} in {locations}")
        jobs = scrape_jobs(keywords, locations, pages)
        
        print(f"\nFound {len(jobs)} jobs")
        
        if jobs:
            print("\nFirst few jobs:")
            for i, job in enumerate(jobs[:5], 1):
                print(f"{i}. {job['title']} at {job['company']} in {job['location']}")
                print(f"   URL: {job['url']}")
                print()
        else:
            print("No jobs found")
            
        return len(jobs) > 0
        
    except Exception as e:
        print(f"Error testing scraper: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_scraper()
    if success:
        print("✅ Scraper test completed successfully!")
    else:
        print("❌ Scraper test failed!")
    
    sys.exit(0 if success else 1)
