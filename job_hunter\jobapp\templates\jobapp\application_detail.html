{% extends 'jobapp/base.html' %}

{% block title %}{{ application.job.title }} Application - Job Hunter{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2>
                <i class="fas fa-clipboard-list"></i> Application Details
            </h2>
            <a href="{% url 'jobapp:application_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Applications
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ application.job.title }}</h5>
            </div>
            <div class="card-body">
                <h6 class="text-muted">{{ application.job.company }}</h6>
                <p><i class="fas fa-map-marker-alt"></i> {{ application.job.location }}</p>
                
                {% if application.job.description %}
                <div class="mt-3">
                    <h6>Job Description:</h6>
                    <p>{{ application.job.description }}</p>
                </div>
                {% endif %}
                
                <div class="mt-3">
                    <a href="{{ application.job.url }}" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> View Original Job Posting
                    </a>
                </div>
            </div>
        </div>
        
        {% if application.cover_letter %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">Generated Cover Letter</h5>
            </div>
            <div class="card-body">
                <pre class="bg-light p-3 rounded">{{ application.cover_letter }}</pre>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Application Status</h5>
            </div>
            <div class="card-body">
                <form method="post" action="{% url 'jobapp:application_update' application.id %}">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">Status:</label>
                        <select class="form-select" id="status" name="status">
                            {% for status_code, status_name in application.STATUS_CHOICES %}
                            <option value="{{ status_code }}" {% if application.status == status_code %}selected{% endif %}>
                                {{ status_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes:</label>
                        <textarea class="form-control" id="notes" name="notes" rows="4">{{ application.notes }}</textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Application
                    </button>
                </form>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">Application Info</h5>
            </div>
            <div class="card-body">
                <p><strong>Resume Used:</strong><br>
                   <a href="{% url 'jobapp:resume_detail' application.resume.id %}">{{ application.resume.title }}</a>
                </p>
                
                {% if application.similarity_score %}
                <p><strong>Match Score:</strong><br>
                   <span class="similarity-score">{{ application.similarity_score|floatformat:1 }}%</span>
                </p>
                {% endif %}
                
                <p><strong>Created:</strong><br>{{ application.created_at|date:"M d, Y H:i" }}</p>
                <p><strong>Updated:</strong><br>{{ application.updated_at|date:"M d, Y H:i" }}</p>
                
                {% if application.applied_date %}
                <p><strong>Applied:</strong><br>{{ application.applied_date|date:"M d, Y H:i" }}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
